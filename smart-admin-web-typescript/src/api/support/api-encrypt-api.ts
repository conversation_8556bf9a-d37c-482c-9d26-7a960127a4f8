/**
 * 接口：加密、解密
 *
 * @Author:    1024创新实验室-主任-卓大
 * @Date:      2023-10-17 20:02:37
 * @Copyright  1024创新实验室
 */
import { postRequest, postEncryptRequest } from '/@/lib/axios';

export const encryptApi = {

  /**
   * 测试 请求加密  <AUTHOR>
   */
  testRequestEncrypt: (param) => {
    return postEncryptRequest('/support/apiEncrypt/testRequestEncrypt', param);
  },

  /**
   * 测试 返回加密  <AUTHOR>
   */
  testResponseEncrypt: (param) => {
    return postRequest('/support/apiEncrypt/testResponseEncrypt', param);
  },

  /**
   * 测试 请求参数加密和解密、返回数据加密和解密  <AUTHOR>
   */
  testDecryptAndEncrypt: (param) => {
    return postEncryptRequest('/support/apiEncrypt/testDecryptAndEncrypt', param);
  },

  /**
   * 测试 数组加解密  <AUTHOR>
   */
  testArray : (param) => {
    return postEncryptRequest('/support/apiEncrypt/testArray', param);
  },
  
};
