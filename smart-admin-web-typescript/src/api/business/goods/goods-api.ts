/*
 * @Description:
 * @Author: zhuoda
 * @Date: 2021-11-05
 * @LastEditTime: 2022-06-23
 * @LastEditors: zhuoda
 */
import {postRequest, getRequest, getDownload} from '/@/lib/axios';

export const goodsApi = {
  // 添加商品 <AUTHOR>
  addGoods: (param) => {
    return postRequest('/goods/add', param);
  },
  // 删除 <AUTHOR>
  deleteGoods: (goodsId) => {
    return getRequest(`/goods/delete/${goodsId}`);
  },
  // 批量 <AUTHOR>
  batchDelete: (goodsIdList) => {
    return postRequest('/goods/batchDelete', goodsIdList);
  },
  // 分页查询 <AUTHOR>
  queryGoodsList: (param) => {
    return postRequest('/goods/query', param);
  },
  // 更新商品 <AUTHOR>
  updateGoods: (param) => {
    return postRequest('/goods/update', param);
  },

  // 导入 <AUTHOR>
  importGoods : (file) =>{
    return postRequest('/goods/importGoods',file);
  },

  // 导出 <AUTHOR>
  exportGoods : () =>{
    return getDownload('/goods/exportGoods');
  }
};
