<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lab1024.sa.base.module.support.heartbeat.HeartBeatRecordDao">


    <update id="updateHeartBeatTimeById">
        update t_heart_beat_record
        set heart_beat_time = #{heartBeatTime}
        <where>
            heart_beat_record_id = #{id}
        </where>
    </update>

    <select id="query" resultType="net.lab1024.sa.base.module.support.heartbeat.domain.HeartBeatRecordEntity">
      select * from t_heart_beat_record where project_path = #{projectPath} and server_ip = #{serverIp} and process_no =#{processNo}
    </select>

    <select id="pageQuery" resultType="net.lab1024.sa.base.module.support.heartbeat.domain.HeartBeatRecordVO">
        SELECT
        *
        FROM
        t_heart_beat_record
        <where>
            <if test="query.startDate != null ">
                AND DATE_FORMAT(heart_beat_time, '%Y-%m-%d') &gt;= #{query.startDate}
            </if>
            <if test="query.endDate != null ">
                AND DATE_FORMAT(heart_beat_time, '%Y-%m-%d') &lt;= #{query.endDate}
            </if>
            <if test="query.keywords != null and query.keywords != ''">
                AND (INSTR(project_path,#{query.keywords}) or INSTR(server_ip,#{query.keywords}) or INSTR(process_no,#{query.keywords}))
            </if>
        </where>
        order by heart_beat_time desc
    </select>

</mapper>
