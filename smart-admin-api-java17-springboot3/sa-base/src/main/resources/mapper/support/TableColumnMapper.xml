<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lab1024.sa.base.module.support.table.TableColumnDao">
    <delete id="deleteTableColumn">
        delete
        from t_table_column
        where user_id = #{userId}
          and table_id = #{tableId}
    </delete>
    <select id="selectByUserIdAndTableId"
            resultType="net.lab1024.sa.base.module.support.table.domain.TableColumnEntity">
        select *
        from t_table_column
        where user_id = #{userId}
          and table_id = #{tableId}
    </select>

</mapper>